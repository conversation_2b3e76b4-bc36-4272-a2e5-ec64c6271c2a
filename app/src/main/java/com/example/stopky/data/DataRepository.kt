package com.example.stopky.data

import android.content.Context
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.*
import android.util.Log

// Datové třídy

data class MaterialRow(
    @SerializedName("norma") val norma: String?, // Opraveno z "norma "
    @SerializedName("izotop") val izotop: String?,
    @SerializedName("material") val material: String?,
    @SerializedName("mu_mm") val mu: String? // Opraveno z "mu_mm "
)
data class IsotopeRow(
    @SerializedName("nazev") val nazev: String?, // Opraveno z "nazev "
    @SerializedName("polocas_dny") val polocas: String?, // Opraveno z "polocas_dny "
    @SerializedName("konst_48") val konst48: String?, // Opraveno z "konst_48 "
    @SerializedName("konst_52") val konst52: String?, // Změněno z Any? na String?
    @SerializedName("konst_59") val konst59: String?, // Změněno z Any? na String?
    @SerializedName("konst_14") val konst14: String?, // Změněno z Any? na String?
    @SerializedName("konst_22") val konst22: String?, // Přidáno pro Se75
    @SerializedName("jednotky") val jednotky: String? // Opraveno z "jednotky "
)
data class FilmRow(
    @SerializedName("nazev") val nazev: String?,
    // R-faktory s novým pojmenováním pro lepší mapování
    @SerializedName("r_faktor_10") val r_faktor_10: String?,
    @SerializedName("r_faktor_15") val r_faktor_15: String?,
    @SerializedName("r_faktor_20") val r_faktor_20: String?,
    @SerializedName("r_faktor_25") val r_faktor_25: String?,
    @SerializedName("r_faktor_30") val r_faktor_30: String?,
    @SerializedName("r_faktor_35") val r_faktor_35: String?,
    @SerializedName("r_faktor_40") val r_faktor_40: String?
)
data class HVLRow(
    @SerializedName("nazev") val nazev: String?,
    @SerializedName("hodnota") val hodnota: String? // Změněno z Any? na String? (pro "0.0" nebo prázdný string)
)
data class ObjectTypeRow(
    @SerializedName("nazev") val nazev: String?,
    @SerializedName("pravidlo_vypoctu") val pravidlo: String? // Opraveno z "pravidlo_vypoctu "
)
data class LocalizationRow(
    @SerializedName("key") val key: String?, // Opraveno z "key "
    @SerializedName("cs") val cs: String?, // Opraveno z "cs "
    @SerializedName("en") val en: String? // Opraveno z "en "
)
// Přidáno pro nové seznamy jednotek
data class UnitSystemRow(@SerializedName("name") val name: String?)
data class ActivityUnitRow(@SerializedName("name") val name: String?)
data class DoseUnitRow(@SerializedName("name") val name: String?)

data class ExposureDataJson(
    @SerializedName("Materials") val materials: List<MaterialRow>?,
    @SerializedName("Isotopes") val isotopes: List<IsotopeRow>?,
    @SerializedName("Films") val films: List<FilmRow>?,
    @SerializedName("HVL") val hvls: List<HVLRow>?,
    @SerializedName("ObjectTypes") val objectTypes: List<ObjectTypeRow>?,
    @SerializedName("Localization") val localization: List<LocalizationRow>?,
    // Přidáno pro nové seznamy jednotek
    @SerializedName("UnitSystems") val unitSystems: List<UnitSystemRow>?,
    @SerializedName("ActivityUnits") val activityUnits: List<ActivityUnitRow>?,
    @SerializedName("DoseUnits") val doseUnits: List<DoseUnitRow>?
)

class DataRepository(private val context: Context) {
    var materials: List<MaterialRow> = emptyList()
        private set
    var isotopes: List<IsotopeRow> = emptyList()
        private set
    var films: List<FilmRow> = emptyList()
        private set
    var hvls: List<HVLRow> = emptyList()
        private set
    var objectTypes: List<ObjectTypeRow> = emptyList()
        private set
    var localization: List<LocalizationRow> = emptyList()
        private set
    // Přidáno pro nové seznamy jednotek
    var unitSystems: List<UnitSystemRow> = emptyList()
        private set
    var activityUnits: List<ActivityUnitRow> = emptyList()
        private set
    var doseUnits: List<DoseUnitRow> = emptyList()
        private set

    private var _isDataLoaded = false
    val isDataLoaded: Boolean get() = _isDataLoaded

    // Callback pro oznámení dokončení načítání
    private var onDataLoadedCallback: (() -> Unit)? = null

    // Synchronní načtení dat z JSON (zachováno pro zpětnou kompatibilitu)
    fun loadJsonData(assetFileName: String = "exposure_data.json") {
        try {
            Log.d("DataRepository", "Začíná synchronní načítání JSON dat...")
            val jsonString = context.assets.open(assetFileName).bufferedReader().use { it.readText() }
            val data = Gson().fromJson(jsonString, ExposureDataJson::class.java)
            updateData(data)
            Log.d("DataRepository", "Synchronní načítání JSON dat dokončeno")
        } catch (e: Exception) {
            Log.e("DataRepository", "Chyba při synchronním načítání JSON dat", e)
            throw e
        }
    }

    // Asynchronní načtení dat z JSON
    suspend fun loadJsonDataAsync(assetFileName: String = "exposure_data.json"): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d("DataRepository", "Začíná asynchronní načítání JSON dat...")
            val jsonString = context.assets.open(assetFileName).bufferedReader().use { it.readText() }
            val data = Gson().fromJson(jsonString, ExposureDataJson::class.java)

            withContext(Dispatchers.Main) {
                updateData(data)
                onDataLoadedCallback?.invoke()
            }

            Log.d("DataRepository", "Asynchronní načítání JSON dat dokončeno")
            true
        } catch (e: Exception) {
            Log.e("DataRepository", "Chyba při asynchronním načítání JSON dat", e)
            false
        }
    }

    // Nastavení callbacku pro oznámení dokončení načítání
    fun setOnDataLoadedCallback(callback: () -> Unit) {
        onDataLoadedCallback = callback
    }

    private fun updateData(data: ExposureDataJson) {
        materials = data.materials ?: emptyList()
        isotopes = data.isotopes ?: emptyList()
        films = data.films ?: emptyList()
        hvls = data.hvls ?: emptyList()
        objectTypes = data.objectTypes ?: emptyList()
        localization = data.localization ?: emptyList()
        // Přidáno pro nové seznamy jednotek
        unitSystems = data.unitSystems ?: emptyList()
        activityUnits = data.activityUnits ?: emptyList()
        doseUnits = data.doseUnits ?: emptyList()
        _isDataLoaded = true
    }
}
