package com.example.stopky.ui.minimumnumberofexposures

import android.content.Context
import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString
import java.io.IOException

object NomogramLoader {
    fun loadNomograms(context: Context): NomogramFile? {
        return try {
            val jsonString = context.assets.open("minimum_exposures_nomogramA1-A4.json").bufferedReader().use { it.readText() }
            Json.decodeFromString<NomogramFile>(jsonString)
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
}
