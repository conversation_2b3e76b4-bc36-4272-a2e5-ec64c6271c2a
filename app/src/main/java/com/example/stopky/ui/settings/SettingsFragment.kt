package com.example.stopky.ui.settings

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.res.Configuration
import android.os.Bundle
import androidx.preference.PreferenceManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.EditText
import android.widget.Spinner
import androidx.fragment.app.Fragment
import com.example.stopky.R
import com.example.stopky.data.DataRepository
import com.example.stopky.data.HVLRow
import com.example.stopky.data.IsotopeRow
import com.example.stopky.data.MaterialRow
import com.example.stopky.data.FilmRow
import com.example.stopky.data.ObjectTypeRow
import com.example.stopky.data.UnitSystemRow
import com.example.stopky.data.ActivityUnitRow
import com.example.stopky.data.DoseUnitRow
import com.example.stopky.databinding.FragmentSettingsBinding
import java.util.Locale

class SettingsFragment : Fragment() {

    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        val view = binding.root

        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val lang = prefs.getString("app_language", "cs") ?: "cs"

        setupScreenSaverSwitch(prefs)
        setupAngleCorrectionSwitch(prefs)
        setupLanguageSpinner(prefs, lang)

        try {
            print("SettingsFragment: INFO - Pokus o načtení JSON dat a nastavení spinnerů.")
            val dataRepo = DataRepository(requireContext())
            dataRepo.loadJsonData()
            print("SettingsFragment: INFO - Načítání JSON dat dokončeno.")

            // Všechna data v pořadí jak jsou v JSON (bez řazení)
            val standards = dataRepo.materials.mapNotNull { it.norma?.trim() }.distinct()
            val currentSelectedStandard = prefs.getString("standard", standards.firstOrNull() ?: "ASTM")
            setupSettingsSpinner(binding.spinnerStandard, standards, currentSelectedStandard, "standard")

            val allIsotopes = dataRepo.isotopes.mapNotNull { it.nazev?.trim() }.distinct()
            val currentSelectedIsotope = prefs.getString("selected_isotope_settings", allIsotopes.firstOrNull())
            setupSettingsSpinner(binding.spinnerIsotopeSettings, allIsotopes, currentSelectedIsotope, "selected_isotope_settings") { selectedIsotope ->
                updateEmissivitySpinnerVisibility(selectedIsotope, prefs)
                updateKGammaSpinnerVisibilityAndContent(selectedIsotope, dataRepo, prefs)
            }

            updateEmissivitySpinnerVisibility(currentSelectedIsotope, prefs)
            updateKGammaSpinnerVisibilityAndContent(currentSelectedIsotope, dataRepo, prefs)

            val unitSystems = dataRepo.unitSystems.mapNotNull { it.name?.trim() }.distinct()
            val currentSelectedUnitSystem = prefs.getString("unit_system", unitSystems.firstOrNull() ?: "Metrický")
            setupSettingsSpinner(binding.spinnerUnitSystem, unitSystems, currentSelectedUnitSystem, "unit_system") { selectedUnitSystem ->
                updateIsotopeSizeUnits(selectedUnitSystem)
            }

            val activityUnits = dataRepo.activityUnits.mapNotNull { it.name?.trim() }.distinct()
            val currentSelectedActivityUnit = prefs.getString("activity_unit", activityUnits.firstOrNull() ?: "GBq")
            setupSettingsSpinner(binding.spinnerActivityUnit, activityUnits, currentSelectedActivityUnit, "activity_unit")

            val doseUnits = dataRepo.doseUnits.mapNotNull { it.name?.trim() }.distinct()
            val currentSelectedDoseUnit = prefs.getString("dose_unit", doseUnits.firstOrNull() ?: "mR")
            setupSettingsSpinner(binding.spinnerDoseUnit, doseUnits, currentSelectedDoseUnit, "dose_unit")

            // HVL Type a Value
            // Odebráno nastavení pro spinnerHvlType, ponecháno pouze pro editTextHvlValue
            val editTextHvlValue = binding.editTextHvlValue
            val customHvlValue = prefs.getString("custom_hvl_value", "") // Načtení uložené custom hodnoty
            editTextHvlValue.setText(customHvlValue)
            editTextHvlValue.addTextChangedListener(object : android.text.TextWatcher {
                override fun afterTextChanged(s: android.text.Editable?) {
                    prefs.edit().putString("custom_hvl_value", s.toString()).apply()
                    // Také uložíme jako "current_hvl_value" pro konzistenci, pokud by to bylo někde očekáváno
                    prefs.edit().putString("current_hvl_value", s.toString()).apply()
                }
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            })

            // Nastavení velikosti izotopu
            setupIsotopeSizeField(prefs)

            // Inicializace jednotek pro velikost izotopu
            val currentUnitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
            updateIsotopeSizeUnits(currentUnitSystem)

            print("SettingsFragment: INFO - Nastavení spinnerů dokončeno úspěšně.")

        } catch (e: Exception) {
            print("SettingsFragment: CHYBA - Kritická chyba během načítání dat nebo nastavení spinnerů: ${e.javaClass.simpleName} - ${e.message}")
            // e.printStackTrace()
        }

        setAppLocale(requireContext(), lang)
        return view
    }

    // Pomocná metoda pro nastavení spinnerů v Settings
    private fun setupSettingsSpinner(
        spinner: Spinner,
        items: List<String>,
        currentSelectedItem: String?,
        preferenceKey: String,
        onItemSelectedAction: ((String) -> Unit)? = null
    ) {
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, items.ifEmpty { listOf(getString(R.string.no_data_available)) })
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
        spinner.isEnabled = items.isNotEmpty()

        val selection = items.indexOf(currentSelectedItem).takeIf { it >= 0 } ?: 0
        if (items.isNotEmpty()) {
             spinner.setSelection(selection)
        }

        spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>, view: View?, pos: Int, id: Long) {
                if (items.isNotEmpty() && pos < items.size) {
                    val selectedValue = items[pos]
                    PreferenceManager.getDefaultSharedPreferences(requireContext()).edit().putString(preferenceKey, selectedValue).apply()
                    onItemSelectedAction?.invoke(selectedValue)
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>) {}
        }
    }

    private fun updateEmissivitySpinnerVisibility(selectedIsotopeName: String?, prefs: SharedPreferences) {
        val labelEmissivity = binding.labelEmissivity
        val spinnerEmissivity = binding.spinnerEmissivity

        if (selectedIsotopeName == "Ir192") {
            labelEmissivity.visibility = View.VISIBLE
            spinnerEmissivity.visibility = View.VISIBLE

            val emissivityValues = listOf("4.8", "5.2", "5.9")
            val currentSelectedEmissivity = prefs.getString("selected_emissivity_ir192", emissivityValues.firstOrNull())

            setupSettingsSpinner(spinnerEmissivity, emissivityValues, currentSelectedEmissivity, "selected_emissivity_ir192") { selectedValue ->
                prefs.edit().putString("selected_emissivity_ir192", selectedValue).apply()
                prefs.edit().putString("current_emissivity_value", selectedValue).apply()
            }
            if (currentSelectedEmissivity != null) {
                prefs.edit().putString("current_emissivity_value", currentSelectedEmissivity).apply()
            } else if (emissivityValues.isNotEmpty()) {
                prefs.edit().putString("current_emissivity_value", emissivityValues.first()).apply()
            }
        } else {
            labelEmissivity.visibility = View.GONE
            spinnerEmissivity.visibility = View.GONE
            prefs.edit().remove("current_emissivity_value").apply()
        }
    }

    private fun updateKGammaSpinnerVisibilityAndContent(selectedIsotopeName: String?, dataRepo: DataRepository, prefs: SharedPreferences) {
        val labelKGamma = binding.textLabelKGammaConstant
        val spinnerKGamma = binding.spinnerKGammaConstant

        if (selectedIsotopeName == "Ir192") {
            labelKGamma.visibility = View.GONE
            spinnerKGamma.visibility = View.GONE
            prefs.edit().remove("current_k_gamma_value").apply()
            return
        }

        labelKGamma.visibility = View.VISIBLE
        spinnerKGamma.visibility = View.VISIBLE

        if (selectedIsotopeName == null) {
            setupSettingsSpinner(spinnerKGamma, emptyList(), null, "selected_k_gamma_constant_value")
            return
        }

        val isotopeData = dataRepo.isotopes.firstOrNull { it.nazev?.trim() == selectedIsotopeName }
        val kGammaConstants = mutableMapOf<String, String>()

        isotopeData?.let { iso ->
            iso.konst48?.takeIf { it.isNotBlank() }?.let { v -> kGammaConstants["K48 (${iso.jednotky?.trim() ?: ""})"] = v.trim() }
            iso.konst52?.takeIf { it.isNotBlank() }?.let { v -> kGammaConstants["K52 (${iso.jednotky?.trim() ?: ""})"] = v.trim() }
            iso.konst59?.takeIf { it.isNotBlank() }?.let { v -> kGammaConstants["K59 (${iso.jednotky?.trim() ?: ""})"] = v.trim() }
            iso.konst14?.takeIf { it.isNotBlank() }?.let { v -> kGammaConstants["K14 (${iso.jednotky?.trim() ?: ""})"] = v.trim() }
            iso.konst22?.takeIf { it.isNotBlank() }?.let { v -> kGammaConstants["K22 (${iso.jednotky?.trim() ?: ""})"] = v.trim() }
        }

        // K-Gamma konstanty v pořadí jak jsou definované (bez řazení)
        val kGammaDisplayItems = kGammaConstants.keys.toList()
        val currentSelectedKGammaValue = prefs.getString("selected_k_gamma_value_for_$selectedIsotopeName", kGammaConstants.values.firstOrNull())
        val currentSelectedKGammaDisplayItem = kGammaConstants.entries.find { it.value == currentSelectedKGammaValue }?.key

        setupSettingsSpinner(spinnerKGamma, kGammaDisplayItems, currentSelectedKGammaDisplayItem, "selected_k_gamma_display_for_$selectedIsotopeName") { selectedDisplayItem ->
            val selectedValue = kGammaConstants[selectedDisplayItem]
            prefs.edit().putString("selected_k_gamma_value_for_$selectedIsotopeName", selectedValue).apply()
            prefs.edit().putString("current_k_gamma_value", selectedValue).apply()
        }
        if (currentSelectedKGammaValue != null) {
             prefs.edit().putString("current_k_gamma_value", currentSelectedKGammaValue).apply()
        } else if (kGammaConstants.values.isNotEmpty()) {
            val firstValue = kGammaConstants.values.first()
            prefs.edit().putString("current_k_gamma_value", firstValue).apply()
            prefs.edit().putString("selected_k_gamma_value_for_$selectedIsotopeName", firstValue).apply()
        }
    }

    private fun setupIsotopeSizeField(prefs: SharedPreferences) {
        val labelIsotopeSize = binding.labelIzotopeSize
        val editIsotopeSize = binding.editIzotopeSize

        // Načtení uložené hodnoty velikosti izotopu (vždy v mm)
        val savedIsotopeSize = prefs.getString("isotope_size_mm", "") ?: ""

        // Zobrazení hodnoty podle aktuálního jednotkového systému
        val currentUnitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val displayValue = convertIsotopeSizeForDisplay(savedIsotopeSize, currentUnitSystem)
        editIsotopeSize.setText(displayValue)

        // Nastavení TextWatcher pro automatické ukládání
        editIsotopeSize.addTextChangedListener(object : android.text.TextWatcher {
            override fun afterTextChanged(s: android.text.Editable?) {
                val inputValue = s.toString().trim()
                if (inputValue.isNotBlank()) {
                    try {
                        // Použijeme Locale.US pro parsování s tečkou jako desetinným oddělovačem
                        val inputDouble = inputValue.replace(',', '.').toDouble()
                        val currentUnitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"

                        // Konverze na mm pro uložení
                        val valueInMm = convertIsotopeSizeToMm(inputDouble, currentUnitSystem)

                        // Uložení v mm (vždy s tečkou)
                        val valueInMmFormatted = String.format(Locale.US, "%.6f", valueInMm)
                        prefs.edit().putString("isotope_size_mm", valueInMmFormatted).apply()
                        prefs.edit().putString("current_isotope_size_mm", valueInMmFormatted).apply()

                        // Uložení také aktuální zobrazované hodnoty (s správným formátováním podle systému)
                        val finalDisplayValue = formatDecimalValue(inputDouble, currentUnitSystem)
                        prefs.edit().putString("current_isotope_size_display", finalDisplayValue).apply()
                        prefs.edit().putString("current_isotope_size_unit", getUnitForSystem(currentUnitSystem)).apply()

                        print("SettingsFragment: INFO - Velikost izotopu uložena: $finalDisplayValue ${getUnitForSystem(currentUnitSystem)} ($valueInMmFormatted mm)")
                    } catch (e: NumberFormatException) {
                        print("SettingsFragment: WARNING - Neplatná hodnota velikosti izotopu: $inputValue")
                    }
                } else {
                    // Vymazání hodnot při prázdném poli
                    prefs.edit().remove("isotope_size_mm").apply()
                    prefs.edit().remove("current_isotope_size_mm").apply()
                    prefs.edit().remove("current_isotope_size_display").apply()
                    prefs.edit().remove("current_isotope_size_unit").apply()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // Pole je vždy viditelné
        labelIsotopeSize.visibility = View.VISIBLE
        editIsotopeSize.visibility = View.VISIBLE

        print("SettingsFragment: INFO - Nastavení velikosti izotopu inicializováno")
    }

    private fun setupHvlSpinners(prefs: SharedPreferences, dataRepo: DataRepository) {
        // Tato metoda již není potřeba v původní podobě, protože spinnerHvlType byl odebrán.
        // Funkcionalita pro editTextHvlValue je nyní přímo v onCreateView.
        // Ponechávám metodu prázdnou nebo ji můžete zcela odstranit, pokud není volána odjinud.
        // Pro jistotu, pokud by byla volána, zajistíme, že nezpůsobí chybu.
        // Logika pro editTextHvlValue byla přesunuta do onCreateView.
    }

    private fun updateHvlEditText(selectedType: String?, editText: EditText, prefs: SharedPreferences, dataRepo: DataRepository) {
        // Tato metoda již není potřeba, protože spinnerHvlType byl odebrán.
        // Logika pro editTextHvlValue byla přesunuta do onCreateView.
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // Listener pro editTextHvlValue je již nastaven v onCreateView
        // val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        // binding.editTextHvlValue.addTextChangedListener(object : android.text.TextWatcher {
        //     override fun afterTextChanged(s: android.text.Editable?) {
        //         // Ukládání hodnoty z editTextHvlValue je již řešeno v onCreateView
        //     }
        //     override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        //     override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        // })
    }

    private fun setupScreenSaverSwitch(prefs: SharedPreferences) {
        val switchScreenSaver = binding.switchScreenSaver
        val screenSaverOn = prefs.getBoolean("screen_saver_on", false)
        switchScreenSaver.isChecked = screenSaverOn
        activity?.window?.apply {
            if (screenSaverOn) addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            else clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
        switchScreenSaver.setOnCheckedChangeListener { _, isChecked ->
            prefs.edit().putBoolean("screen_saver_on", isChecked).apply()
            activity?.window?.apply {
                if (isChecked) addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                else clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }
    }

    private fun setupAngleCorrectionSwitch(prefs: SharedPreferences) {
        val switchAngleCorrection = binding.switchAngleCorrection
        val angleCorrectionOn = prefs.getBoolean("angle_correction_enabled", true) // Výchozí: zapnuto
        switchAngleCorrection.isChecked = angleCorrectionOn

        switchAngleCorrection.setOnCheckedChangeListener { _, isChecked ->
            prefs.edit().putBoolean("angle_correction_enabled", isChecked).apply()
        }
    }

    private fun setupLanguageSpinner(prefs: SharedPreferences, currentLangCode: String) {
        val spinner = binding.spinnerLanguage
        val languages = resources.getStringArray(R.array.language_names)
        val codes = resources.getStringArray(R.array.language_codes)
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, languages)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
        spinner.setSelection(codes.indexOf(currentLangCode).takeIf { it >= 0 } ?: 0)
        spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>, view: View?, position: Int, id: Long) {
                val selectedCode = codes[position]
                if (selectedCode != prefs.getString("app_language", "cs")) {
                    prefs.edit().putString("app_language", selectedCode).apply()
                    // Restart aplikace pro aplikaci jazyka
                    val intent = requireActivity().packageManager.getLaunchIntentForPackage(requireActivity().packageName)
                    // Oprava: Použití ?.let pro bezpečné spuštění aktivity
                    intent?.let { nonNullIntent ->
                        nonNullIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(nonNullIntent)
                        requireActivity().finishAffinity()
                    }
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>) {}
        }
    }

    private fun setAppLocale(context: Context, language: String) {
        val locale = Locale(language)
        Locale.setDefault(locale)
        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)
        // context.createConfigurationContext(config) // Tato řádka může být nadbytečná nebo způsobovat problémy v některých kontextech
        context.resources.updateConfiguration(config, context.resources.displayMetrics)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun updateIsotopeSizeUnits(unitSystem: String) {
        val labelIsotopeSize = binding.labelIzotopeSize
        val editIsotopeSize = binding.editIzotopeSize
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())

        // Získání aktuální hodnoty v mm
        val currentValueMm = prefs.getString("isotope_size_mm", "") ?: ""

        // Aktualizace labelu s jednotkami
        val unit = getUnitForSystem(unitSystem)
        labelIsotopeSize.text = getString(R.string.label_isotope_size, unit)

        // Konverze a zobrazení hodnoty v nových jednotkách
        if (currentValueMm.isNotBlank()) {
            try {
                val valueInMm = currentValueMm.toDouble()
                val convertedValue = convertIsotopeSizeFromMm(valueInMm, unitSystem)
                val formattedValue = formatDecimalValue(convertedValue, unitSystem)
                editIsotopeSize.setText(formattedValue)
            } catch (e: NumberFormatException) {
                // Pokud konverze selže, ponecháme pole prázdné
                editIsotopeSize.setText("")
            }
        }

        print("SettingsFragment: INFO - Jednotky velikosti izotopu aktualizovány na: $unit")
    }

    private fun getUnitForSystem(unitSystem: String): String {
        return when (unitSystem) {
            "Metrický" -> "mm"
            "Americký", "Britský" -> "inch"
            else -> "mm" // Výchozí
        }
    }

    private fun convertIsotopeSizeToMm(value: Double, unitSystem: String): Double {
        return when (unitSystem) {
            "Metrický" -> value // Už je v mm
            "Americký", "Britský" -> value * 25.4 // inch na mm
            else -> value // Výchozí - předpokládáme mm
        }
    }

    private fun convertIsotopeSizeFromMm(valueInMm: Double, unitSystem: String): Double {
        return when (unitSystem) {
            "Metrický" -> valueInMm // Zůstává v mm
            "Americký", "Britský" -> valueInMm / 25.4 // mm na inch
            else -> valueInMm // Výchozí - zůstává v mm
        }
    }

    private fun convertIsotopeSizeForDisplay(savedValue: String, unitSystem: String): String {
        if (savedValue.isBlank()) return ""

        return try {
            val valueInMm = savedValue.toDouble()
            val convertedValue = convertIsotopeSizeFromMm(valueInMm, unitSystem)
            formatDecimalValue(convertedValue, unitSystem)
        } catch (e: NumberFormatException) {
            ""
        }
    }

    /**
     * Formátuje desetinnou hodnotu pro zobrazení podle jednotkového systému
     * - Metrický systém (mm): 1 desetinné místo (evropský standard)
     * - Americký/Britský systém (inch): 3 desetinná místa (pro přesnost)
     * - Používá tečku jako desetinný oddělovač (pro konzistenci s výpočty)
     */
    private fun formatDecimalValue(value: Double, unitSystem: String): String {
        // Použijeme Locale.US pro zajištění tečky jako desetinného oddělovače
        val formatted = when (unitSystem) {
            "Metrický" -> {
                // Pro mm: zaokrouhlíme na 1 desetinné místo
                String.format(Locale.US, "%.1f", value)
            }
            "Americký", "Britský" -> {
                // Pro inch: zaokrouhlíme na 3 desetinná místa
                String.format(Locale.US, "%.3f", value).trimEnd('0').let { trimmed ->
                    if (trimmed.endsWith('.')) "${trimmed}0" else trimmed
                }
            }
            else -> {
                // Výchozí: jako metrický
                String.format(Locale.US, "%.1f", value)
            }
        }

        return formatted
    }

    /**
     * Přetížená metoda pro zpětnou kompatibilitu
     */
    private fun formatDecimalValue(value: Double): String {
        return formatDecimalValue(value, "Metrický")
    }

    companion object {
        // Konstanty pro konverzi
        private const val MM_PER_INCH = 25.4

        /**
         * Pomocná metoda pro získání velikosti izotopu z SharedPreferences v mm
         * @param context Context aplikace
         * @return Velikost izotopu v mm jako Double, nebo null pokud není nastavena nebo je neplatná
         */
        fun getIsotopeSizeMm(context: Context): Double? {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val sizeString = prefs.getString("current_isotope_size_mm", "") ?: ""
            return try {
                if (sizeString.isNotBlank()) {
                    sizeString.toDouble()
                } else {
                    null
                }
            } catch (e: NumberFormatException) {
                null
            }
        }

        /**
         * Pomocná metoda pro získání velikosti izotopu v aktuálních jednotkách
         * @param context Context aplikace
         * @return Velikost izotopu v aktuálních jednotkách jako Double, nebo null pokud není nastavena
         */
        fun getIsotopeSize(context: Context): Double? {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val displayValue = prefs.getString("current_isotope_size_display", "") ?: ""
            return try {
                if (displayValue.isNotBlank()) {
                    displayValue.toDouble()
                } else {
                    null
                }
            } catch (e: NumberFormatException) {
                null
            }
        }

        /**
         * Pomocná metoda pro získání velikosti izotopu jako String v aktuálních jednotkách
         * @param context Context aplikace
         * @return Velikost izotopu jako String s jednotkami
         */
        fun getIsotopeSizeString(context: Context): String {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val displayValue = prefs.getString("current_isotope_size_display", "") ?: ""
            val unit = prefs.getString("current_isotope_size_unit", "mm") ?: "mm"

            return if (displayValue.isNotBlank()) {
                "$displayValue $unit"
            } else {
                ""
            }
        }

        /**
         * Pomocná metoda pro získání jednotky velikosti izotopu
         * @param context Context aplikace
         * @return Jednotka velikosti izotopu (mm nebo inch)
         */
        fun getIsotopeSizeUnit(context: Context): String {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            return prefs.getString("current_isotope_size_unit", "mm") ?: "mm"
        }
    }
}
