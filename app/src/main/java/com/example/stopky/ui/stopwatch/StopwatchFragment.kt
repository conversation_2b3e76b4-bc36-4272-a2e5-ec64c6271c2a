package com.example.stopky.ui.stopwatch

import android.os.Bundle
import android.os.SystemClock
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.example.stopky.databinding.FragmentStopwatchBinding
import com.example.stopky.R

class StopwatchFragment : Fragment() {
    private var binding: FragmentStopwatchBinding? = null
    private val viewModel: StopwatchViewModel by viewModels()
    private var handler: Handler? = null
    private var startTime: Long = 0L
    private var pauseOffset: Long = 0L

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentStopwatchBinding.inflate(inflater, container, false)
        val adapter = StopwatchHistoryAdapter { selectedTime ->
            // případně nějak<PERSON> akce po kliknutí na historii
        }
        binding!!.recyclerViewHistory.adapter = adapter

        viewModel.elapsedTime.observe(viewLifecycleOwner) {
            binding!!.textViewStopwatch.text = formatTime(it)
        }
        viewModel.isRunning.observe(viewLifecycleOwner) { running ->
            binding!!.buttonStartStopwatch.text = getString(if (running) R.string.stop else R.string.start)
            binding!!.buttonPauseStopwatch.isEnabled = running
        }
        viewModel.isPaused.observe(viewLifecycleOwner) { paused ->
            binding!!.buttonPauseStopwatch.text = getString(if (paused) R.string.resume else R.string.pause)
        }
        viewModel.history.observe(viewLifecycleOwner) {
            adapter.submitList(it.toList())
        }

        binding!!.buttonStartStopwatch.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            if (viewModel.isRunning.value != true) {
                viewModel.setRunning(true)
                viewModel.setPaused(false)
                startTime = SystemClock.elapsedRealtime() - pauseOffset
                startTimer()
            } else {
                viewModel.setRunning(false)
                viewModel.setPaused(false)
                stopTimer()
                pauseOffset = 0L
                viewModel.addToHistory(viewModel.elapsedTime.value ?: 0)
                //viewModel.setElapsedTime(0)
            }
        }

        binding!!.buttonPauseStopwatch.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            if (viewModel.isRunning.value == true && viewModel.isPaused.value != true) {
                viewModel.setPaused(true)
                stopTimer()
                pauseOffset = SystemClock.elapsedRealtime() - startTime
            } else if (viewModel.isRunning.value == true && viewModel.isPaused.value == true) {
                viewModel.setPaused(false)
                startTime = SystemClock.elapsedRealtime() - pauseOffset
                startTimer()
            }
        }

        binding!!.buttonResetStopwatch.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            viewModel.setRunning(false)
            viewModel.setPaused(false)
            stopTimer()
            pauseOffset = 0L
            viewModel.setElapsedTime(0)
        }

        return binding!!.root
    }

    // OPRAVA ANR: Optimalizovaný timer s menší frekvencí aktualizace
    private fun startTimer() {
        handler = Handler(Looper.getMainLooper())
        handler?.post(object : Runnable {
            override fun run() {
                if (viewModel.isRunning.value == true && viewModel.isPaused.value != true) {
                    val elapsed = SystemClock.elapsedRealtime() - startTime
                    viewModel.setElapsedTime(elapsed)
                    // OPRAVA ANR: Změna z 100ms na 250ms pro snížení zátěže CPU
                    handler?.postDelayed(this, 250)
                }
            }
        })
    }

    private fun stopTimer() {
        handler?.removeCallbacksAndMessages(null)
    }

    private fun formatTime(ms: Long): String {
        val totalSeconds = ms / 1000
        val h = totalSeconds / 3600
        val m = (totalSeconds % 3600) / 60
        val s = totalSeconds % 60
        return String.format("%02d:%02d:%02d", h, m, s)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        stopTimer()
        binding = null
    }
}