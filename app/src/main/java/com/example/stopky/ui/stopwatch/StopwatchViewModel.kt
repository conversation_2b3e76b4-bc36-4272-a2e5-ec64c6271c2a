package com.example.stopky.ui.stopwatch

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData

class StopwatchViewModel(app: Application) : AndroidViewModel(app) {
    private val prefs = app.getSharedPreferences("stopwatch_prefs", Context.MODE_PRIVATE)

    private val _elapsedTime = MutableLiveData<Long>(0)
    val elapsedTime: LiveData<Long> = _elapsedTime

    private val _isRunning = MutableLiveData(false)
    val isRunning: LiveData<Boolean> = _isRunning

    private val _isPaused = MutableLiveData(false)
    val isPaused: LiveData<Boolean> = _isPaused

    private val _history = MutableLiveData<MutableList<Long>>(loadHistory())
    val history: LiveData<MutableList<Long>> = _history

    fun setElapsedTime(time: Long) {
        _elapsedTime.value = time
    }

    fun setRunning(running: Boolean) {
        _isRunning.value = running
    }

    fun setPaused(paused: <PERSON>olean) {
        _isPaused.value = paused
    }

    fun addToHistory(time: Long) {
        if (time > 0) {
            _history.value!!.add(0, time)
            if (_history.value!!.size > 10) _history.value!!.removeAt(10)
            saveHistory()
            _history.value = _history.value
        }
    }

    private fun saveHistory() {
        prefs.edit().putStringSet("history", _history.value!!.map { it.toString() }.toSet()).apply()
    }

    private fun loadHistory(): MutableList<Long> {
        return prefs.getStringSet("history", emptySet())?.map { it.toLong() }?.toMutableList() ?: mutableListOf()
    }
}