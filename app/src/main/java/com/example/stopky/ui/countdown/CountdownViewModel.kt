package com.example.stopky.ui.countdown

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData

class CountdownViewModel(app: Application) : AndroidViewModel(app) {
    private val prefs = app.getSharedPreferences("countdown_prefs", Context.MODE_PRIVATE)

    private val _timeLeft = MutableLiveData<Long>(0)
    val timeLeft: LiveData<Long> = _timeLeft

    private val _isRunning = MutableLiveData(false)
    val isRunning: LiveData<Boolean> = _isRunning

    private val _isPaused = MutableLiveData(false)
    val isPaused: LiveData<Boolean> = _isPaused

    private val _history = MutableLiveData<List<Long>>()
    val history: LiveData<List<Long>> = _history

    var initialSetTime: Long = 0
    var lastSetTime: Long = 0

    init {
        _history.value = loadHistory()
    }

    fun setTime(time: Long) {
        _timeLeft.value = time
        lastSetTime = time
        initialSetTime = time
    }

    fun updateTimeLeft(time: Long) {
        _timeLeft.value = time
    }

    fun setRunning(running: Boolean) {
        _isRunning.value = running
    }

    fun setPaused(paused: Boolean) {
        _isPaused.value = paused
    }

    fun addToHistory(time: Long) {
        if (time <= 0) return
        val current = _history.value?.toMutableList() ?: mutableListOf()
        if (current.firstOrNull() == time) return // stejný čas už je první, nic nedělej
        current.remove(time)
        current.add(0, time)
        if (current.size > 30) current.removeAt(30) // počet položek historie
        _history.value = current
        saveHistory(current)
    }

    fun clearHistory() {
        _history.value = emptyList()
        prefs.edit().remove("history").commit()
    }

    private fun saveHistory(list: List<Long>) {
        prefs.edit().putString("history", list.joinToString(",")).commit()
    }

    private fun loadHistory(): List<Long> {
        val historyString = prefs.getString("history", null)
        if (historyString != null && historyString.isNotEmpty()) {
            return historyString.split(",").mapNotNull { it.toLongOrNull() }
        }
        return emptyList()
    }
}