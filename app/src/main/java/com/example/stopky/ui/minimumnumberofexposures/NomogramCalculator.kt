package com.example.stopky.ui.minimumnumberofexposures

import kotlin.math.ceil

class NomogramCalculator(private val nomogramData: NomogramData) {

    private fun interpolate(x: Float, x1: Float, y1: Float, x2: Float, y2: Float): Float {
        if (x1 == x2) return y1
        return y1 + (x - x1) * (y2 - y1) / (x2 - x1)
    }

    private fun findYOnLine(lineN: List<Point>, x_in: Float): Float {
        if (lineN.isEmpty()) return Float.NaN

        val p1 = lineN.lastOrNull { it.x <= x_in }
        val p2 = lineN.firstOrNull { it.x >= x_in }

        return when {
            p1 == null -> lineN.first().y
            p2 == null -> lineN.last().y
            else -> interpolate(x_in, p1.x, p1.y, p2.x, p2.y)
        }
    }

    fun calculateN(t: Float, De: Float, distance: Float): Int {
        if (De == 0f || distance == 0f) return -1 // Chybný vstup

        val x_in = t / De
        val y_in = De / distance

        val sortedNValues = nomogramData.curves.keys.mapNotNull { it.toIntOrNull() }.sorted()

        // 1. KROK: První interpolace - najdeme Y pro každou křivku N pro dané x_in
        val yValuesForXin = sortedNValues.associateWith { n ->
            val curve = nomogramData.curves[n.toString()]
            if (curve != null) findYOnLine(curve, x_in) else Float.NaN
        }.filterValues { !it.isNaN() }

        if (yValuesForXin.isEmpty()) return -1 // Nelze provést výpočet

        // --- OPRAVENÁ LOGIKA PRO OKRAJOVÉ PODMÍNKY ---
        // Najdeme křivku s nejvyšší a nejnižší hodnotou Y
        val maxY = yValuesForXin.values.maxOrNull()!!
        val minY = yValuesForXin.values.minOrNull()!!

        // Pokud je naše hodnota y_in nad nejvyšší křivkou
        if (y_in >= maxY) {
            // Najdeme, které N odpovídá této nejvyšší křivce a vrátíme ho
            return yValuesForXin.filterValues { it == maxY }.keys.minOrNull() ?: -1
        }
        // Pokud je naše hodnota y_in pod nejnižší křivkou
        if (y_in < minY) {
            // Najdeme, které N odpovídá této nejnižší křivce a vrátíme ho
            // (v praxi by se nemělo stát, že potřebujeme více expozic než je na grafu)
            return yValuesForXin.filterValues { it == minY }.keys.maxOrNull() ?: -1
        }
        // --- KONEC OPRAVENÉ LOGIKY ---


        // 2. KROK: Druhá interpolace - najdeme, mezi které N spadá naše y_in
        var nLower = -1
        var yLower = Float.NaN
        var nUpper = -1
        var yUpper = Float.NaN

        // Musíme iterovat přes křivky seřazené podle Y, ne podle N
        val sortedByY = yValuesForXin.entries.sortedBy { it.value }

        for (entry in sortedByY) {
            if (entry.value <= y_in) {
                nLower = entry.key
                yLower = entry.value
            }
            if (entry.value >= y_in && nUpper == -1) {
                nUpper = entry.key
                yUpper = entry.value
            }
        }

        if (nLower == nUpper) return nLower

        val fractionalN = interpolate(y_in, yLower, nLower.toFloat(), yUpper, nUpper.toFloat())

        // 3. KROK: Zaokrouhlení nahoru
        return ceil(fractionalN).toInt()
    }
}
