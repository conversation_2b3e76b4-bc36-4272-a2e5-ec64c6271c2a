package com.example.stopky.ui.countdown

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import com.example.stopky.R
import com.example.stopky.databinding.DialogAlarmBinding

class AlarmDialogFragment : DialogFragment() {
    
    private var binding: DialogAlarmBinding? = null
    private var mediaPlayer: MediaPlayer? = null
    private var vibrator: Vibrator? = null
    private var wakeLock: PowerManager.WakeLock? = null
    private var handler: Handler? = null
    private var blinkRunnable: Runnable? = null
    private var alarmRunnable: Runnable? = null
    private var isBlinkRed = true
    private var currentVolume = 1.0f
    
    companion object {
        fun newInstance(volume: Float): AlarmDialogFragment {
            val fragment = AlarmDialogFragment()
            val args = Bundle()
            args.putFloat("volume", volume)
            fragment.arguments = args
            return fragment
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.Theme_Stopky_AlarmDialog)
        isCancelable = false // Nelze zrušit jinak než tlačítkem Stop

        currentVolume = arguments?.getFloat("volume", 1.0f) ?: 1.0f
    }
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        // Nastavení pro zobrazení přes lock screen
        dialog.window?.let { window ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                // Pro Android 8.1+ používáme setShowWhenLocked na aktivitě
                requireActivity().setShowWhenLocked(true)
                requireActivity().setTurnScreenOn(true)
            } else {
                @Suppress("DEPRECATION")
                window.addFlags(
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                )
            }
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }

        return dialog
    }
    
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DialogAlarmBinding.inflate(inflater, container, false)
        return binding!!.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Inicializace služeb
        initializeServices()
        
        // Nastavení tlačítka Stop
        binding!!.buttonStopAlarm.setOnClickListener {
            stopAlarm()
            dismiss()
        }
        
        // Spuštění alarmu
        startAlarm()
    }
    
    private fun initializeServices() {
        // WakeLock pro probuzení telefonu
        val powerManager = requireContext().getSystemService(Context.POWER_SERVICE) as PowerManager
        @Suppress("DEPRECATION")
        wakeLock = powerManager.newWakeLock(
            PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
            "Stopky:AlarmWakeLock"
        )
        wakeLock?.acquire(5 * 60 * 1000L) // 5 minut maximum

        // Vibrator
        vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = requireContext().getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            @Suppress("DEPRECATION")
            requireContext().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }

        // Handler pro opakování
        handler = Handler(Looper.getMainLooper())
    }
    
    private fun startAlarm() {
        startBlinking()
        startRepeatingAlarm()
    }
    
    private fun startBlinking() {
        blinkRunnable = object : Runnable {
            override fun run() {
                binding?.let { binding ->
                    val colorRes = if (isBlinkRed) {
                        R.color.alarm_background
                    } else {
                        R.color.alarm_background_blink
                    }
                    val color = ContextCompat.getColor(requireContext(), colorRes)
                    binding.alarmDialogBackground.setBackgroundColor(color)
                    isBlinkRed = !isBlinkRed
                    handler?.postDelayed(this, 500) // Blikání každých 500ms
                }
            }
        }
        handler?.post(blinkRunnable!!)
    }
    
    private fun startRepeatingAlarm() {
        alarmRunnable = object : Runnable {
            override fun run() {
                playAlarmSound()
                vibrate()
                handler?.postDelayed(this, 2000) // Opakování každé 2 sekundy
            }
        }
        handler?.post(alarmRunnable!!)
    }
    
    private fun playAlarmSound() {
        try {
            mediaPlayer?.release()
            mediaPlayer = MediaPlayer.create(requireContext(), R.raw.alarm_sound)
            mediaPlayer?.setVolume(currentVolume, currentVolume)
            mediaPlayer?.start()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun vibrate() {
        try {
            vibrator?.let { vib ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val vibrationEffect = VibrationEffect.createWaveform(longArrayOf(0, 500, 200, 500), -1)
                    vib.vibrate(vibrationEffect)
                } else {
                    @Suppress("DEPRECATION")
                    vib.vibrate(longArrayOf(0, 500, 200, 500), -1)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun stopAlarm() {
        // Zastavení všech runnable
        handler?.removeCallbacks(blinkRunnable ?: return)
        handler?.removeCallbacks(alarmRunnable ?: return)
        
        // Uvolnění MediaPlayer
        mediaPlayer?.release()
        mediaPlayer = null
        
        // Uvolnění WakeLock
        wakeLock?.release()
        wakeLock = null
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        stopAlarm()
        binding = null
    }
}
