package com.example.stopky.ui.matcalc

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.stopky.databinding.FragmentMatCalcBinding
import net.objecthunter.exp4j.ExpressionBuilder
import net.objecthunter.exp4j.function.Function

class MatCalcFragment : Fragment() {
    private var binding: FragmentMatCalcBinding? = null
    private val history = mutableListOf<String>()
    private lateinit var adapter: HistoryAdapter

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        adapter = HistoryAdapter(history)
        binding?.recyclerViewHistory?.adapter = adapter
        binding?.recyclerViewHistory?.layoutManager = LinearLayoutManager(context)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentMatCalcBinding.inflate(inflater, container, false)

        val grid = binding?.gridLayoutKeyboard
        if (grid != null) {
            for (i in 0 until grid.childCount) {
                val v = grid.getChildAt(i)
                if (v is Button) {
                    when (v.text) {
                        "C" -> v.setOnClickListener { clearAll() }
                        "=" -> v.setOnClickListener { evaluate() }
                        "⌫" -> v.setOnClickListener { backspace() }
                        else -> v.setOnClickListener { appendToExpression(v.text.toString()) }
                    }
                }
            }
        }
        return binding!!.root
    }

    private fun appendToExpression(str: String) {
        val expr = binding?.textViewExpression?.text?.toString() ?: ""
        val newExpr = if (expr == "0") str else expr + str
        binding?.textViewExpression?.text = newExpr
        updatePreviewResult(newExpr)
    }

    private fun updatePreviewResult(expr: String) {
        try {
            val result = evaluateExpression(expr)
            binding?.textViewResult?.text = result?.toString() ?: ""
        } catch (e: Exception) {
            binding?.textViewResult?.text = ""
        }
    }

    private fun evaluate() {
        val expr = binding?.textViewExpression?.text?.toString() ?: ""
        try {
            val result = evaluateExpression(expr)
            if (result != null) {
                binding?.textViewResult?.text = result.toString()
                history.add(0, "$expr = $result")
                adapter.notifyItemInserted(0)
                binding?.recyclerViewHistory?.scrollToPosition(0)
                binding?.textViewExpression?.text = "0"
            }
        } catch (e: Exception) {
            binding?.textViewResult?.text = "Chyba"
        }
    }

    private fun clearAll() {
        binding?.textViewExpression?.text = "0"
        binding?.textViewResult?.text = ""
    }

    private fun backspace() {
        val expr = binding?.textViewExpression?.text?.toString() ?: ""
        if (expr.isNotEmpty() && expr != "0") {
            val newExpr = expr.dropLast(1)
            binding?.textViewExpression?.text = if (newExpr.isEmpty()) "0" else newExpr
            updatePreviewResult(binding?.textViewExpression?.text?.toString() ?: "")
        }
    }

    private fun evaluateExpression(expr: String): Double? {
        val prepared = expr
            .replace("÷", "/")
            .replace("×", "*")
            .replace("−", "-")
            .replace("π", Math.PI.toString())
            .replace("e", Math.E.toString())
            .replace("%", "/100")
            .replace("√", "sqrt")
            .replace("X^", "^")
            .replace(",", ".")
        val sinFunction = object : Function("sin", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.sin(args[0])
        }
        val cosFunction = object : Function("cos", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.cos(args[0])
        }
        val tanFunction = object : Function("tan", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.tan(args[0])
        }
        val logFunction = object : Function("log", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.log10(args[0])
        }
        val expFunction = object : Function("exp", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.exp(args[0])
        }
        val absFunction = object : Function("abs", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.abs(args[0])
        }
        val sqrtFunction = object : Function("sqrt", 1) {
            override fun apply(args: DoubleArray): Double = kotlin.math.sqrt(args[0])
        }
        val factFunction = object : Function("fact", 1) {
            override fun apply(args: DoubleArray): Double = factorial(args[0])
        }
        val expression = ExpressionBuilder(prepared)
            .functions(
                sinFunction, cosFunction, tanFunction, logFunction,
                expFunction, absFunction, sqrtFunction, factFunction
            )
            .build()
        return expression.evaluate()
    }

    private fun factorial(a: Double): Double {
        if (a < 0 || a != a.toInt().toDouble()) return Double.NaN
        var result = 1.0
        for (i in 1..a.toInt()) result *= i
        return result
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }
}