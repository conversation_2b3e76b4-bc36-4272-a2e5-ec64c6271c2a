package com.example.stopky.ui.countdown

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.Button
import android.widget.NumberPicker
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import com.example.stopky.R

class TimePickerDialogFragment(
    private val onTimeSet: (h: Int, m: Int, s: Int) -> Unit
) : DialogFragment() {
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_time_picker, null)
        val npH = view.findViewById<NumberPicker>(R.id.npHours)
        val npM = view.findViewById<NumberPicker>(R.id.npMinutes)
        val npS = view.findViewById<NumberPicker>(R.id.npSeconds)
        npH.minValue = 0; npH.maxValue = 23
        npM.minValue = 0; npM.maxValue = 59
        npS.minValue = 0; npS.maxValue = 59

        return AlertDialog.Builder(requireContext())
            .setView(view)
            .setCancelable(true)
            .create().apply {
                view.findViewById<Button>(R.id.buttonConfirm).setOnClickListener {
                    onTimeSet(npH.value, npM.value, npS.value)
                    dismiss()
                }
            }
    }
}