package com.example.stopky.ui.countdown

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.example.stopky.databinding.FragmentCountdownBinding
import com.example.stopky.R
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import android.widget.SeekBar
import android.media.MediaPlayer

class CountdownFragment : Fragment() {
    private var timer: CountDownTimer? = null
    private var binding: FragmentCountdownBinding? = null
    private val viewModel: CountdownViewModel by viewModels()
    private var mediaPlayer: MediaPlayer? = null
    private var isMuted = false

    private fun playAlarm() {
        if (isMuted) return
        mediaPlayer = MediaPlayer.create(requireContext(), R.raw.alarm_sound) // alarm_sound.mp3 v res/raw
        mediaPlayer?.setVolume(currentVolume, currentVolume)
        mediaPlayer?.start()
    }

    private fun showAlarmDialog() {
        if (isMuted) return
        val alarmDialog = AlarmDialogFragment.newInstance(currentVolume)
        alarmDialog.show(parentFragmentManager, "AlarmDialog")
    }
    private var currentVolume = 1.0f // 0.0 - 1.0

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentCountdownBinding.inflate(inflater, container, false)
        val adapter = HistoryAdapter { selectedTime ->
            viewModel.setTime(selectedTime)
        }
        binding!!.recyclerViewHistory.layoutManager = GridLayoutManager(requireContext(), 3) // 3 sloupce
        binding!!.recyclerViewHistory.adapter = adapter

        // Pozorování LiveData
        viewModel.timeLeft.observe(viewLifecycleOwner) {
            binding!!.textViewCountdown.text = formatTime(it)
        }
        viewModel.isRunning.observe(viewLifecycleOwner) { running ->
            binding!!.buttonStartCountdown.text = getString(if (running) R.string.stop else R.string.start)
            binding!!.buttonPauseCountdown.isEnabled = running
        }
        viewModel.isPaused.observe(viewLifecycleOwner) { paused ->
            binding!!.buttonPauseCountdown.text = getString(if (paused) R.string.resume else R.string.pause)
        }
        viewModel.history.observe(viewLifecycleOwner) {
            adapter.submitList(it.toList())
        }

        binding!!.seekBarVolume.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                currentVolume = progress / 100f
                if (progress == 0) {
                    binding!!.buttonVolume.setImageResource(R.drawable.ic_volume_off_48px)
                } else {
                    binding!!.buttonVolume.setImageResource(R.drawable.ic_volume_up_48px)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        binding!!.buttonVolume.setOnClickListener {
            binding!!.seekBarVolume.visibility =
                if (binding!!.seekBarVolume.visibility == View.VISIBLE) View.GONE else View.VISIBLE
        }

        binding!!.buttonClearHistory.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            viewModel.clearHistory()
        }

        binding!!.buttonStartCountdown.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            if (viewModel.isRunning.value != true) {
                viewModel.setRunning(true)
                viewModel.setPaused(false)
                startTimer()
            } else {
                viewModel.setRunning(false)
                viewModel.setPaused(false)
                timer?.cancel()
                viewModel.updateTimeLeft(viewModel.lastSetTime)
            }
        }

        binding!!.buttonPauseCountdown.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            if (viewModel.isRunning.value == true && viewModel.isPaused.value != true) {
                viewModel.setPaused(true)
                timer?.cancel()
            } else if (viewModel.isRunning.value == true && viewModel.isPaused.value == true) {
                viewModel.setPaused(false)
                startTimer()
            }
        }

        binding!!.buttonResetCountdown.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            viewModel.setRunning(false)
            viewModel.setPaused(false)
            timer?.cancel()
            viewModel.updateTimeLeft(viewModel.initialSetTime)
        }

        binding!!.textViewCountdown.setOnClickListener {
            it.performHapticFeedback(android.view.HapticFeedbackConstants.KEYBOARD_TAP)
            it.playSoundEffect(android.view.SoundEffectConstants.CLICK)
            TimePickerDialogFragment { h, m, s ->
                val input = h * 3600 + m * 60 + s
                val ms = input * 1000L
                viewModel.setTime(ms)
                viewModel.updateTimeLeft(ms)
            }.show(parentFragmentManager, "timePicker")
        }

        return binding!!.root
    }

    private fun startTimer() {
        timer = object : CountDownTimer(viewModel.timeLeft.value ?: 0, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                viewModel.updateTimeLeft(millisUntilFinished)
            }
            override fun onFinish() {
                showAlarmDialog() // zobrazí alarm dialog
                viewModel.setRunning(false)
                viewModel.setPaused(false)
                viewModel.updateTimeLeft(0)
                viewModel.addToHistory(viewModel.lastSetTime)
            }
        }.start()
    }

    private fun formatTime(ms: Long): String {
        val totalSeconds = ms / 1000
        val h = totalSeconds / 3600
        val m = (totalSeconds % 3600) / 60
        val s = totalSeconds % 60
        return String.format("%02d:%02d:%02d", h, m, s)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mediaPlayer?.release()
        mediaPlayer = null
        timer?.cancel()
        binding = null
    }
}