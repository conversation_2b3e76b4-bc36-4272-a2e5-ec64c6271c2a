package com.example.stopky.ui.unsharp

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import android.widget.Spinner
import android.widget.ArrayAdapter
import android.widget.AdapterView
import androidx.fragment.app.Fragment
import androidx.preference.PreferenceManager
import com.example.stopky.R
import android.content.Context
import android.content.SharedPreferences
import java.util.Locale

class UnsharpFragment : Fragment(), SharedPreferences.OnSharedPreferenceChangeListener {

    private lateinit var inputF: EditText
    private lateinit var inputA: EditText
    private lateinit var inputB: EditText
    private lateinit var textResult: TextView

    // TextView labely pro dynamické jednotky
    private lateinit var labelFocalSpotSize: TextView
    private lateinit var labelDistanceSourceSubject: TextView
    private lateinit var labelDistanceSubjectFilm: TextView
    private lateinit var labelGeometricUnsharpness: TextView

    // Minimální vzdálenost komponenty
    private lateinit var inputIsotopeSizeD: EditText
    private lateinit var spinnerTestingClass: Spinner
    private lateinit var inputObjectFilmDistanceB: EditText
    private lateinit var textMinDistanceResult: TextView
    private lateinit var textTotalDistanceResult: TextView
    private lateinit var labelIsotopeSizeD: TextView
    private lateinit var labelObjectFilmDistanceB: TextView
    private lateinit var labelMinDistanceResult: TextView
    private lateinit var labelTotalDistanceResult: TextView

    // TextWatcher pro sledování změn
    private lateinit var textWatcher: TextWatcher
    private lateinit var minDistanceTextWatcher: TextWatcher

    // Flag pro dočasné zakázání TextWatcher
    private var isUpdatingValues = false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_unsharp, container, false)
        inputF = view.findViewById(R.id.inputF)
        inputA = view.findViewById(R.id.inputA)
        inputB = view.findViewById(R.id.inputB)
        textResult = view.findViewById(R.id.textResult)

        // Inicializace TextView labelů
        labelFocalSpotSize = view.findViewById(R.id.labelFocalSpotSize)
        labelDistanceSourceSubject = view.findViewById(R.id.labelDistanceSourceSubject)
        labelDistanceSubjectFilm = view.findViewById(R.id.labelDistanceSubjectFilm)
        labelGeometricUnsharpness = view.findViewById(R.id.labelGeometricUnsharpness)

        // Inicializace komponent pro minimální vzdálenost
        inputIsotopeSizeD = view.findViewById(R.id.inputIsotopeSizeD)
        spinnerTestingClass = view.findViewById(R.id.spinnerTestingClass)
        inputObjectFilmDistanceB = view.findViewById(R.id.inputObjectFilmDistanceB)
        textMinDistanceResult = view.findViewById(R.id.textMinDistanceResult)
        textTotalDistanceResult = view.findViewById(R.id.textTotalDistanceResult)
        labelIsotopeSizeD = view.findViewById(R.id.labelIsotopeSizeD)
        labelObjectFilmDistanceB = view.findViewById(R.id.labelObjectFilmDistanceB)
        labelMinDistanceResult = view.findViewById(R.id.labelMinDistanceResult)
        labelTotalDistanceResult = view.findViewById(R.id.labelTotalDistanceResult)

        // Aktualizace jednotek v labelech
        updateLabels()

        // Nastavení spinneru pro třídu zkoušky
        setupTestingClassSpinner()

        // Inicializace TextWatcher
        textWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (!isUpdatingValues) {
                    saveInputsInMm()
                    calculateResult()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        // TextWatcher pro minimální vzdálenost
        minDistanceTextWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (!isUpdatingValues) {
                    saveMinDistanceInputs()
                    calculateMinDistance()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        inputF.addTextChangedListener(textWatcher)
        inputA.addTextChangedListener(textWatcher)
        inputB.addTextChangedListener(textWatcher)

        inputIsotopeSizeD.addTextChangedListener(minDistanceTextWatcher)
        inputObjectFilmDistanceB.addTextChangedListener(minDistanceTextWatcher)

        // Načtení uložených hodnot (jednoduché načtení z SharedPreferences)
        loadValues()
        loadMinDistanceValues()

        // OPRAVA: Spustit výpočet po načtení uložených hodnot
        calculateResult()
        calculateMinDistance()

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Nastavení rozbalitelné karty po vytvoření view
        setupExpandableCard()
    }

    override fun onResume() {
        super.onResume()
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        prefs.registerOnSharedPreferenceChangeListener(this)

        // Detekce změny jednotek při návratu do fragmentu
        checkAndHandleUnitChange()
    }

    override fun onPause() {
        super.onPause()
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        prefs.unregisterOnSharedPreferenceChangeListener(this)
    }

    // Implementace SharedPreferences listeneru
    override fun onSharedPreferenceChanged(sharedPreferences: SharedPreferences?, key: String?) {
        if (key == "unit_system") {
            updateLabels()
            clearAllValues()
        }
    }

    private fun updateLabels() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unit = when (unitSystem) {
            "Metrický" -> "mm"
            "Americký", "Britský" -> "inch"
            else -> "mm"
        }

        labelFocalSpotSize.text = getString(R.string.label_focal_spot_size, unit)
        labelDistanceSourceSubject.text = getString(R.string.label_distance_source_subject, unit)
        labelDistanceSubjectFilm.text = getString(R.string.label_distance_subject_film, unit)
        labelGeometricUnsharpness.text = getString(R.string.label_geometric_unsharpness, unit)

        // Aktualizace labelů pro minimální vzdálenost
        labelIsotopeSizeD.text = getString(R.string.label_isotope_size_d, unit)
        labelObjectFilmDistanceB.text = getString(R.string.label_object_film_distance_b, unit)
        labelMinDistanceResult.text = getString(R.string.label_min_distance_result, unit)
        labelTotalDistanceResult.text = getString(R.string.label_total_distance_result, unit)
    }

    private fun clearAllValues() {
        inputF.setText("")
        inputA.setText("")
        inputB.setText("")
        textResult.text = "0.00"

        // Vymazání hodnot pro minimální vzdálenost
        inputIsotopeSizeD.setText("")
        inputObjectFilmDistanceB.setText("")
        textMinDistanceResult.text = "0.00"
        textTotalDistanceResult.text = "0.00"
    }

    private fun checkAndHandleUnitChange() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val currentUnitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unsharpPrefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        val lastUnitSystem = unsharpPrefs.getString("last_unit_system", "") ?: ""

        // Aktualizace labelů vždy
        updateLabels()

        // Pokud se jednotkový systém změnil, vymaž hodnoty
        if (lastUnitSystem.isNotEmpty() && lastUnitSystem != currentUnitSystem) {
            clearAllValues()
        }

        // Uložení aktuálního jednotkového systému
        unsharpPrefs.edit().putString("last_unit_system", currentUnitSystem).apply()
    }

    private fun saveInputsInMm() {
        if (isUpdatingValues) return

        // Jednoduché uložení hodnot do SharedPreferences (bez konverze)
        val prefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        prefs.edit()
            .putString("inputF", inputF.text.toString())
            .putString("inputA", inputA.text.toString())
            .putString("inputB", inputB.text.toString())
            .apply()
    }

    private fun loadValues() {
        isUpdatingValues = true

        // Jednoduché načtení hodnot ze SharedPreferences
        val prefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        inputF.setText(prefs.getString("inputF", ""))
        inputA.setText(prefs.getString("inputA", ""))
        inputB.setText(prefs.getString("inputB", ""))
        textResult.text = prefs.getString("result", "0.00")

        isUpdatingValues = false
    }



    private fun calculateResult() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unsharpPrefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)

        // Načtení aktuálních hodnot z EditText polí
        val f = inputF.text.toString().replace(',', '.').toDoubleOrNull()
        val a = inputA.text.toString().replace(',', '.').toDoubleOrNull()
        val b = inputB.text.toString().replace(',', '.').toDoubleOrNull()

        if (f != null && a != null && b != null && a != 0.0) {
            // Konverze vstupních hodnot na mm pro výpočet
            val fMm = convertToMm(f, unitSystem)
            val aMm = convertToMm(a, unitSystem)
            val bMm = convertToMm(b, unitSystem)

            // Výpočet v mm
            val resultMm = (fMm * bMm) / aMm

            // Konverze výsledku do aktuálního jednotkového systému
            val resultInCurrentUnit = convertFromMm(resultMm, unitSystem)

            val resultText = formatValue(resultInCurrentUnit, unitSystem)
            textResult.text = resultText

            // Uložit výsledek do SharedPreferences
            unsharpPrefs.edit().putString("result", resultText).apply()
        } else {
            textResult.text = "0.00"
            unsharpPrefs.edit().putString("result", "0.00").apply()
        }
    }

    // Jednoduché lokální funkce pro konverzi
    private fun convertToMm(value: Double, unitSystem: String): Double {
        return when (unitSystem) {
            "Metrický" -> value
            "Americký", "Britský" -> value * 25.4
            else -> value
        }
    }

    private fun convertFromMm(valueInMm: Double, unitSystem: String): Double {
        return when (unitSystem) {
            "Metrický" -> valueInMm
            "Americký", "Britský" -> valueInMm / 25.4
            else -> valueInMm
        }
    }

    private fun formatValue(value: Double, unitSystem: String): String {
        return when (unitSystem) {
            "Metrický" -> String.format(Locale.US, "%.1f", value)
            "Americký", "Britský" -> String.format(Locale.US, "%.3f", value)
            else -> String.format(Locale.US, "%.1f", value)
        }
    }

    private fun setupExpandableCard() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())

        // Nastavení karty Unsharp
        setupSingleExpandableCard(
            R.id.cardHeaderUnsharp,
            R.id.cardContentUnsharp,
            R.id.iconUnsharpExpand,
            "unsharp_card_expanded",
            true, // Výchozí: rozbaleno
            prefs
        )

        // Nastavení karty Minimální vzdálenost
        setupSingleExpandableCard(
            R.id.cardHeaderMinDistance,
            R.id.cardContentMinDistance,
            R.id.iconMinDistanceExpand,
            "min_distance_card_expanded",
            false, // Výchozí: sbaleno
            prefs
        )
    }

    private fun setupSingleExpandableCard(
        headerResId: Int,
        contentResId: Int,
        iconResId: Int,
        prefKey: String,
        defaultExpanded: Boolean,
        prefs: SharedPreferences
    ) {
        val cardHeader = view?.findViewById<View>(headerResId)
        val cardContent = view?.findViewById<View>(contentResId)
        val expandIcon = view?.findViewById<View>(iconResId)

        // Kontrola, zda byly view nalezeny
        if (cardHeader == null || cardContent == null || expandIcon == null) {
            return
        }

        // Načíst uložený stav karty
        val isExpanded = prefs.getBoolean(prefKey, defaultExpanded)

        // Nastavit počáteční stav
        cardContent.visibility = if (isExpanded) View.VISIBLE else View.GONE
        expandIcon.rotation = if (isExpanded) 180f else 0f

        // Nastavit click listener
        cardHeader.setOnClickListener {
            val isCurrentlyExpanded = cardContent.visibility == View.VISIBLE
            val newExpanded = !isCurrentlyExpanded

            // Animace rozbalení/sbalení
            if (newExpanded) {
                cardContent.visibility = View.VISIBLE
                expandIcon.animate().rotation(180f).duration = 200
            } else {
                cardContent.visibility = View.GONE
                expandIcon.animate().rotation(0f).duration = 200
            }

            // Uložit stav
            prefs.edit().putBoolean(prefKey, newExpanded).apply()
        }
    }

    private fun setupTestingClassSpinner() {
        val classes = arrayOf(
            getString(R.string.class_a),
            getString(R.string.class_b)
        )

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, classes)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerTestingClass.adapter = adapter

        spinnerTestingClass.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (!isUpdatingValues) {
                    saveMinDistanceInputs()
                    calculateMinDistance()
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }

    private fun saveMinDistanceInputs() {
        if (isUpdatingValues) return

        val prefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        prefs.edit()
            .putString("inputIsotopeSizeD", inputIsotopeSizeD.text.toString())
            .putString("inputObjectFilmDistanceB", inputObjectFilmDistanceB.text.toString())
            .putInt("spinnerTestingClass", spinnerTestingClass.selectedItemPosition)
            .apply()
    }

    private fun loadMinDistanceValues() {
        isUpdatingValues = true

        val prefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        inputIsotopeSizeD.setText(prefs.getString("inputIsotopeSizeD", ""))
        inputObjectFilmDistanceB.setText(prefs.getString("inputObjectFilmDistanceB", ""))
        spinnerTestingClass.setSelection(prefs.getInt("spinnerTestingClass", 0))
        textMinDistanceResult.text = prefs.getString("minDistanceResult", "0.00")
        textTotalDistanceResult.text = prefs.getString("totalDistanceResult", "0.00")

        isUpdatingValues = false
    }

    private fun calculateMinDistance() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unsharpPrefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)

        // Načtení aktuálních hodnot z EditText polí
        val d = inputIsotopeSizeD.text.toString().replace(',', '.').toDoubleOrNull()
        val b = inputObjectFilmDistanceB.text.toString().replace(',', '.').toDoubleOrNull()
        val classPosition = spinnerTestingClass.selectedItemPosition

        if (d != null && b != null) {
            // Konverze vstupních hodnot na mm pro výpočet
            val dMm = convertToMm(d, unitSystem)
            val bMm = convertToMm(b, unitSystem)

            // Výpočet podle vzorce
            val resultMm = if (classPosition == 0) {
                // Třída A: f/d ≥ 7.5b^(2/3)
                7.5 * Math.pow(bMm, 2.0/3.0) * dMm
            } else {
                // Třída B: f/d ≥ 15b^(2/3)
                15.0 * Math.pow(bMm, 2.0/3.0) * dMm
            }

            // Konverze výsledku do aktuálního jednotkového systému
            val resultInCurrentUnit = convertFromMm(resultMm, unitSystem)

            val resultText = formatValue(resultInCurrentUnit, unitSystem)
            textMinDistanceResult.text = resultText

            // Výpočet celkové vzdálenosti (f + b)
            val totalDistanceMm = resultMm + bMm
            val totalDistanceInCurrentUnit = convertFromMm(totalDistanceMm, unitSystem)
            val totalDistanceText = formatValue(totalDistanceInCurrentUnit, unitSystem)
            textTotalDistanceResult.text = totalDistanceText

            // Uložit výsledky do SharedPreferences
            unsharpPrefs.edit()
                .putString("minDistanceResult", resultText)
                .putString("totalDistanceResult", totalDistanceText)
                .apply()
        } else {
            textMinDistanceResult.text = "0.00"
            textTotalDistanceResult.text = "0.00"
            unsharpPrefs.edit()
                .putString("minDistanceResult", "0.00")
                .putString("totalDistanceResult", "0.00")
                .apply()
        }
    }

}