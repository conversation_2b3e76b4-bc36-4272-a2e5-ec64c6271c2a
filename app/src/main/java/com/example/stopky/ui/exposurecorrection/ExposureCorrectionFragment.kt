package com.example.stopky.ui.exposurecorrection

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.preference.PreferenceManager
import com.example.stopky.R
import kotlin.math.pow

class ExposureCorrectionFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_exposurecorrection, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Nastavení rozbalovacích karet
        setupExpandableCards()

        // Prvn<PERSON> (zčernání)
        val inputExposure1 = view.findViewById<EditText>(R.id.inputExposure1)
        val inputDensity1 = view.findViewById<EditText>(R.id.inputDensity1)
        val inputDensity2 = view.findViewById<EditText>(R.id.inputDensity2)
        val textExposure2 = view.findViewById<TextView>(R.id.textExposure2)

        // Druhá kalkulačka (vzdálenost)
        val inputExposure1Dist = view.findViewById<EditText>(R.id.inputExposure1Dist)
        val inputDistance1 = view.findViewById<EditText>(R.id.inputDistance1)
        val inputDistance2 = view.findViewById<EditText>(R.id.inputDistance2)
        val textExposure2Dist = view.findViewById<TextView>(R.id.textExposure2Dist)

        // Načtení uložených hodnot
        val prefs = requireContext().getSharedPreferences("exposure_correction_prefs", Context.MODE_PRIVATE)
        inputExposure1.setText(prefs.getString("exposure1", ""))
        inputDensity1.setText(prefs.getString("density1", ""))
        inputDensity2.setText(prefs.getString("density2", ""))
        inputExposure1Dist.setText(prefs.getString("exposure1_dist", ""))
        inputDistance1.setText(prefs.getString("distance1", ""))
        inputDistance2.setText(prefs.getString("distance2", ""))

        // Načtení uložených výsledků
        textExposure2.text = prefs.getString("result_density", "0.00")
        textExposure2Dist.text = prefs.getString("result_distance", "0.00")

        val watcher1 = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                // Uložit hodnoty
                prefs.edit()
                    .putString("exposure1", inputExposure1.text.toString())
                    .putString("density1", inputDensity1.text.toString())
                    .putString("density2", inputDensity2.text.toString())
                    .apply()

                val exposure1 = inputExposure1.text.toString().toDoubleOrNull()
                val density1 = inputDensity1.text.toString().toDoubleOrNull()
                val density2 = inputDensity2.text.toString().toDoubleOrNull()
                val result = if (exposure1 != null && density1 != null && density2 != null && density1 != 0.0) {
                    exposure1 * (density2 / density1)
                } else {
                    0.0
                }
                val resultText = String.format("%.2f", result)
                textExposure2.text = resultText
                prefs.edit().putString("result_density", resultText).apply()
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        inputExposure1.addTextChangedListener(watcher1)
        inputDensity1.addTextChangedListener(watcher1)
        inputDensity2.addTextChangedListener(watcher1)

        val watcher2 = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                // Uložit hodnoty
                prefs.edit()
                    .putString("exposure1_dist", inputExposure1Dist.text.toString())
                    .putString("distance1", inputDistance1.text.toString())
                    .putString("distance2", inputDistance2.text.toString())
                    .apply()

                val exposure1 = inputExposure1Dist.text.toString().toDoubleOrNull()
                val distance1 = inputDistance1.text.toString().toDoubleOrNull()
                val distance2 = inputDistance2.text.toString().toDoubleOrNull()
                val result = if (exposure1 != null && distance1 != null && distance2 != null && distance1 != 0.0) {
                    exposure1 * (distance2 / distance1).pow(2.0)
                } else {
                    0.0
                }
                val resultText = String.format("%.2f", result)
                textExposure2Dist.text = resultText
                prefs.edit().putString("result_distance", resultText).apply()
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        inputExposure1Dist.addTextChangedListener(watcher2)
        inputDistance1.addTextChangedListener(watcher2)
        inputDistance2.addTextChangedListener(watcher2)

        // OPRAVA: Spustit výpočty po načtení uložených hodnot
        watcher1.afterTextChanged(null)
        watcher2.afterTextChanged(null)
    }

    private fun setupExpandableCards() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())

        // Nastavení první karty (korekce hustoty)
        setupSingleExpandableCard(
            R.id.cardHeaderDensity,
            R.id.cardContentDensity,
            R.id.iconExpandDensity,
            "density_card_expanded",
            false, // Výchozí: sbaleno
            prefs
        )

        // Nastavení druhé karty (korekce vzdálenosti)
        setupSingleExpandableCard(
            R.id.cardHeaderDistance,
            R.id.cardContentDistance,
            R.id.iconExpandDistance,
            "distance_card_expanded",
            false, // Výchozí: sbaleno
            prefs
        )
    }

    private fun setupSingleExpandableCard(
        headerResId: Int,
        contentResId: Int,
        iconResId: Int,
        prefKey: String,
        defaultExpanded: Boolean,
        prefs: SharedPreferences
    ) {
        val cardHeader = view?.findViewById<View>(headerResId)
        val cardContent = view?.findViewById<View>(contentResId)
        val expandIcon = view?.findViewById<View>(iconResId)

        // Načíst uložený stav karty
        val isExpanded = prefs.getBoolean(prefKey, defaultExpanded)

        // Nastavit počáteční stav
        cardContent?.visibility = if (isExpanded) View.VISIBLE else View.GONE
        expandIcon?.rotation = if (isExpanded) 180f else 0f

        // Nastavit click listener
        cardHeader?.setOnClickListener {
            val isCurrentlyExpanded = cardContent?.visibility == View.VISIBLE
            val newExpanded = !isCurrentlyExpanded

            // Animace rozbalení/sbalení
            if (newExpanded) {
                cardContent?.visibility = View.VISIBLE
                expandIcon?.animate()?.rotation(180f)?.duration = 200
            } else {
                cardContent?.visibility = View.GONE
                expandIcon?.animate()?.rotation(0f)?.duration = 200
            }

            // Uložit stav
            prefs.edit().putBoolean(prefKey, newExpanded).apply()
        }
    }
}