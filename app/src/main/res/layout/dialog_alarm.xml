<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:id="@+id/alarmDialogBackground"
    android:background="@color/alarm_background">

    <ImageView
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/ic_alarm_24"
        android:layout_marginBottom="16dp"
        android:contentDescription="@string/alarm_title" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/alarm_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp"
        android:gravity="center" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/alarm_message"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:layout_marginBottom="24dp"
        android:gravity="center" />

    <Button
        android:id="@+id/buttonStopAlarm"
        android:layout_width="200dp"
        android:layout_height="80dp"
        android:text="@string/alarm_stop"
        android:textSize="18sp"
        android:textStyle="bold"
        android:backgroundTint="#D7A91C"
        android:textColor="@color/black"
        android:layout_gravity="center" />

</LinearLayout>
