<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Vstupní parametry Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="14dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#262525">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="5dp">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginBottom="16dp">

        <ImageView
            android:id="@+id/imageLogo"
            android:layout_width="360dp"
            android:layout_height="123dp"
            android:layout_gravity="center"
            android:contentDescription="Logo"
            android:src="@drawable/ic_logo" />

    </LinearLayout>

    <TextView
        android:id="@+id/textViewCountdown"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="00:00:00"
        android:textSize="72sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="8dp"
        style="@style/Text.Result"/>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="8dp">

        <Button
            android:id="@+id/buttonStartCountdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/start"
            style="@style/AppButton.Result" />

        <Button
            android:id="@+id/buttonPauseCountdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pause"
            android:layout_marginStart="8dp"
            style="@style/AppButton.Result" />

        <Button
            android:id="@+id/buttonResetCountdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/reset"
            android:layout_marginStart="8dp"
            style="@style/AppButton.Result" />

        <ImageButton
            android:id="@+id/buttonVolume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="48dp"
            android:maxHeight="48dp"
            android:src="@drawable/ic_volume_up_48px"
            android:backgroundTint="@color/result_text"
            app:tint="@color/result_text"
            android:contentDescription="@string/volume"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:layout_marginStart="8dp" />
    </LinearLayout>

    <SeekBar
        android:id="@+id/seekBarVolume"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="100"
        android:rotation="0"
        android:padding="16dp"
        android:visibility="gone"
        android:progressTint="@color/result_text"
        android:thumbTint="@color/dirty_yellow"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewHistory"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:paddingTop="20dp"
        android:scrollbars="vertical"
        android:background="@color/history_bg"
        android:padding="10dp" />


    <Button
        android:id="@+id/buttonClearHistory"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/button_clear_history"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        style="@style/AppButton.Result"/>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</LinearLayout>