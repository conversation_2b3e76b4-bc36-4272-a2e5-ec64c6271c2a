<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="12dp">

    <!-- Displej -->
    <TextView
        android:id="@+id/textViewExpression"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="0"
        android:textSize="32sp"
        android:textAlignment="viewEnd"
        android:padding="8dp"
        android:background="?attr/colorSurface"
        android:elevation="2dp"
        android:layout_marginBottom="4dp"
        style="@style/Text.Result"/>

    <!-- Výsledek -->
    <TextView
        android:id="@+id/textViewResult"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="0"
        android:textSize="20sp"
        android:textAlignment="viewEnd"
        android:padding="8dp"
        android:background="?attr/colorSurface"
        android:elevation="1dp"
        android:layout_marginBottom="8dp"
        style="@style/Text.Result"/>

    <!-- Historie -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewHistory"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="8dp"
        android:layout_weight="1"
        android:background="@color/history_bg"
        android:padding="4dp"
        android:scrollbars="vertical"
        android:fadingEdge="vertical"
        android:scrollbarThumbVertical="@android:color/darker_gray"
        android:scrollbarAlwaysDrawVerticalTrack="true" />

    <!-- Klávesnice -->
    <GridLayout
        android:id="@+id/gridLayoutKeyboard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:rowCount="7"
        android:columnCount="5"
        android:layout_gravity="center"
        android:padding="0dp" >

        <!-- 1. řádek: paměťové operace -->
        <Button android:text="M+" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="M-" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="MR" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="MC" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="C" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>

        <!-- 2. řádek: trigonometrie -->
        <Button android:text="sin" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="cos" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="tan" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="π" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="deg" style="@style/CalcFunButton" android:id="@+id/buttonDegRad" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>

        <!-- 3. řádek -->
        <Button android:text="sin⁻¹" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="cos⁻¹" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="tan⁻¹" style="@style/CalcFunButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="(" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text=")" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>

        <!-- 4. řádek -->
        <Button android:text="7" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="8" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="9" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="X^" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="√" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>

        <!-- 5. řádek -->
        <Button android:text="4" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="5" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="6" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="×" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="÷" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>

        <!-- 6. řádek -->
        <Button android:text="1" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="2" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="3" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="+" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="−" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>

        <!-- 7. řádek -->
        <Button android:text="0" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="." style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="⌫" style="@style/CalcOpButton" android:layout_width="0dp" android:layout_height="0dp"
            android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="%" style="@style/CalcOpButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
        <Button android:text="=" style="@style/CalcNumButton" android:layout_width="0dp"
            android:layout_height="0dp" android:layout_rowWeight="1" android:layout_columnWeight="1" android:layout_margin="2dp"/>
    </GridLayout>
</LinearLayout>