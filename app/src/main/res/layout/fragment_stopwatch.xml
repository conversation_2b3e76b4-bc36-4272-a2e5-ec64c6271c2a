<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Vstupní parametry Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="14dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#262525">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="5dp">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginBottom="16dp">

        <ImageView
            android:id="@+id/imageLogo"
            android:layout_width="360dp"
            android:layout_height="123dp"
            android:layout_gravity="center"
            android:contentDescription="Logo"
            android:src="@drawable/ic_logo" />

    </LinearLayout>

    <TextView
        android:id="@+id/textViewStopwatch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="00:00:00"
        android:textSize="72sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="16dp"
        style="@style/Text.Result"/>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="24dp">

        <Button
            android:id="@+id/buttonStartStopwatch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/start"
            style="@style/AppButton.Result" />

        <Button
            android:id="@+id/buttonPauseStopwatch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pause"
            android:layout_marginStart="8dp"
            style="@style/AppButton.Result" />

        <Button
            android:id="@+id/buttonResetStopwatch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/reset"
            android:layout_marginStart="8dp"
            style="@style/AppButton.Result" />
    </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewHistory"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingTop="16dp" />

</LinearLayout>