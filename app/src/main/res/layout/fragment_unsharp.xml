<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <!-- Vstupní parametry Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="14dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#262525">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp">

        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> karty s tlačítkem pro rozbalení/sbalení -->
        <LinearLayout
            android:id="@+id/cardHeaderUnsharp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_unsharp_48px"
                app:tint="@android:color/white"
                android:layout_marginEnd="8dp"
                android:contentDescription="@string/unsharp_title" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/unsharp_title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/white" />

            <ImageView
                android:id="@+id/iconUnsharpExpand"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_expand_more"
                app:tint="@android:color/white"
                android:contentDescription="@string/content_desc_expand_collapse" />

        </LinearLayout>

        <!-- Obsah karty (rozbalitelný) -->
        <LinearLayout
            android:id="@+id/cardContentUnsharp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Velikost ohniska -->
                <TextView
                    android:id="@+id/labelFocalSpotSize"
                    android:layout_width="220dp"
                    android:layout_height="wrap_content"
                    android:text="@string/label_focal_spot_size"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="5dp" />

                <EditText
                    android:id="@+id/inputF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp"
                    android:inputType="numberDecimal"
                    android:textSize="20sp"
                    android:gravity="center"
                    android:hint="F"
                    android:textColor="@android:color/white"
                    android:padding="8dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Vzdalenost zdroj-objekt -->
                <TextView
                    android:id="@+id/labelDistanceSourceSubject"
                    android:layout_width="220dp"
                    android:layout_height="wrap_content"
                    android:text="@string/label_distance_source_subject"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="5dp" />

                <EditText
                    android:id="@+id/inputA"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp"
                    android:inputType="numberDecimal"
                    android:textSize="20sp"
                    android:gravity="center"
                    android:hint="a"
                    android:textColor="@android:color/white"
                    android:padding="8dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Vzdalenost objekt-film -->
                <TextView
                    android:id="@+id/labelDistanceSubjectFilm"
                    android:layout_width="220dp"
                    android:layout_height="wrap_content"
                    android:text="@string/label_distance_subject_film"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="5dp" />

                <EditText
                    android:id="@+id/inputB"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp"
                    android:inputType="numberDecimal"
                    android:textSize="20sp"
                    android:gravity="center"
                    android:hint="b"
                    android:textColor="@android:color/white"
                    android:padding="8dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Výsledek -->
                <TextView
                    android:id="@+id/labelGeometricUnsharpness"
                    android:layout_width="220dp"
                    android:layout_height="wrap_content"
                    android:text="@string/label_geometric_unsharpness"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="5dp" />

                <TextView
                    android:id="@+id/textResult"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="0.00"
                    android:textSize="30sp"
                    android:textStyle="bold"
                    android:gravity="center_horizontal"
                    style="@style/Text.Result"
                    android:layout_marginBottom="12dp"
                    android:padding="8dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Obrázek schématu -->
                <ImageView
                    android:id="@+id/imageScheme"
                    android:layout_width="match_parent"
                    android:layout_height="340dp"
                    android:src="@drawable/unsharp_scheme"
                    android:scaleType="fitCenter"
                    android:contentDescription="@string/unsharp_scheme_desc"
                    android:layout_marginTop="10dp"
                    android:padding="16dp" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Minimální vzdálenost Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="14dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#262525">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="5dp">

            <!-- Hlavička karty s tlačítkem pro rozbalení/sbalení -->
            <LinearLayout
                android:id="@+id/cardHeaderMinDistance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground"
                android:padding="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_menu_calculate_48px"
                    app:tint="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:contentDescription="@string/min_distance_title" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/min_distance_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

                <ImageView
                    android:id="@+id/iconMinDistanceExpand"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_expand_more"
                    app:tint="@android:color/white"
                    android:contentDescription="@string/content_desc_expand_collapse" />

            </LinearLayout>

            <!-- Obsah karty (rozbalitelný) -->
            <LinearLayout
                android:id="@+id/cardContentMinDistance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                    <!-- Velikost izotopu -->
                    <TextView
                        android:id="@+id/labelIsotopeSizeD"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_isotope_size_d"
                        android:textSize="14sp"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="5dp" />

                    <EditText
                        android:id="@+id/inputIsotopeSizeD"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginBottom="12dp"
                        android:inputType="numberDecimal"
                        android:textSize="20sp"
                        android:gravity="center"
                        android:hint="@string/hint_isotope_size"
                        android:textColor="@android:color/white"
                        android:padding="8dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                    <!-- Třída zkoušky -->
                    <TextView
                        android:id="@+id/labelTestingClass"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_testing_class"
                        android:textSize="14sp"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="5dp" />

                    <Spinner
                        android:id="@+id/spinnerTestingClass"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginBottom="12dp"
                        android:padding="8dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                    <!-- Vzdálenost objekt-film -->
                    <TextView
                        android:id="@+id/labelObjectFilmDistanceB"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_object_film_distance_b"
                        android:textSize="14sp"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="5dp" />

                    <EditText
                        android:id="@+id/inputObjectFilmDistanceB"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginBottom="12dp"
                        android:inputType="numberDecimal"
                        android:textSize="20sp"
                        android:gravity="center"
                        android:hint="@string/hint_object_film_distance"
                        android:textColor="@android:color/white"
                        android:padding="8dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                    <!-- Výsledek -->
                    <TextView
                        android:id="@+id/labelMinDistanceResult"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_min_distance_result"
                        android:textSize="14sp"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="5dp" />

                    <TextView
                        android:id="@+id/textMinDistanceResult"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="0.00"
                        android:textSize="30sp"
                        android:textStyle="bold"
                        android:gravity="center_horizontal"
                        style="@style/Text.Result"
                        android:layout_marginBottom="12dp"
                        android:padding="8dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                    <!-- Celková vzdálenost -->
                    <TextView
                        android:id="@+id/labelTotalDistanceResult"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_total_distance_result"
                        android:textSize="14sp"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="5dp" />

                    <TextView
                        android:id="@+id/textTotalDistanceResult"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="0.00"
                        android:textSize="30sp"
                        android:textStyle="bold"
                        android:gravity="center_horizontal"
                        style="@style/Text.Result"
                        android:layout_marginBottom="12dp"
                        android:padding="8dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                    <!-- Obrázek schématu minimální vzdálenosti -->
                    <ImageView
                        android:id="@+id/imageFminimalScheme"
                        android:layout_width="match_parent"
                        android:layout_height="340dp"
                        android:src="@drawable/fminimal"
                        android:scaleType="fitCenter"
                        android:contentDescription="@string/fminimal_desc"
                        android:layout_marginTop="10dp"
                        android:padding="16dp" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>