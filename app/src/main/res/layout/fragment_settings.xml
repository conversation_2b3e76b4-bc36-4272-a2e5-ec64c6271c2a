<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- Rozbalova<PERSON><PERSON> karta pro Wire IQI Sensitivity -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="14dp"
        app:cardBackgroundColor="#262525"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Logo nahrazeno TextView kvůli problémům s drawable -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚙️ NASTAVENÍ"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:textColor="@color/result_text" />

        <!-- Switch pro spořič obrazovky -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="160dp"
                android:layout_height="wrap_content"
                android:text="@string/switch_screen_saver"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <Switch
                android:id="@+id/switchScreenSaver"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:minHeight="48dp"
                android:thumbTint="@color/result_text"
                android:trackTint="@color/light_yellow"/>

        </LinearLayout>

        <!-- Switch pro úhlovou korekci -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="160dp"
                android:layout_height="wrap_content"
                android:text="@string/switch_angle_correction"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <Switch
                android:id="@+id/switchAngleCorrection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:minHeight="48dp"
                android:thumbTint="@color/result_text"
                android:trackTint="@color/light_yellow"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <TextView
            android:id="@+id/textViewLanguage"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical"
            android:text="@string/select_language" />

        <Spinner
            android:id="@+id/spinnerLanguage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp"
            android:popupBackground="@color/light_yellow_popup" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr normy -->
        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_standard"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />
        <Spinner
            android:id="@+id/spinnerStandard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr Izotopu (přesunuto sem, pokud ještě není) -->
        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_isotope_type"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />
        <Spinner
            android:id="@+id/spinnerIsotopeSettings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <!-- Výběr velikosti izotopu -->
            <TextView
                android:id="@+id/labelIzotopeSize"
                android:layout_width="160dp"
                android:layout_height="wrap_content"
                android:text="@string/label_isotope_size"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical"
                android:visibility="visible" />

            <EditText
                android:id="@+id/editIzotopeSize"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberDecimal"
                android:hint="@string/hint_IzotopeSize"
                android:minHeight="48dp"
                android:textSize="16sp"
                android:gravity="center"
                android:textColor="@android:color/white"
                android:padding="8dp"
                android:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr Emisivity -->
        <TextView
            android:id="@+id/labelEmissivity"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_emissivity"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical"
            android:visibility="gone" />
        <Spinner
            android:id="@+id/spinnerEmissivity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp"
            android:visibility="gone" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr K-Gamma konstanty -->
        <TextView
            android:id="@+id/textLabelKGammaConstant"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_k_gamma_constant"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />
        <Spinner
            android:id="@+id/spinnerKGammaConstant"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr jednotkového systému -->
        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_unit_system"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />
        <Spinner
            android:id="@+id/spinnerUnitSystem"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr jednotek aktivity -->
        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_activity_unit"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />
        <Spinner
            android:id="@+id/spinnerActivityUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr jednotek dávky -->
        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_dose_unit"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />
        <Spinner
            android:id="@+id/spinnerDoseUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

        <!-- Výběr HVL -->
        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/label_hvl_collimator_custom_instruction"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:gravity="center_vertical" />

        <EditText
            android:id="@+id/editTextHvlValue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:inputType="numberDecimal"
            android:hint="@string/hint_hvl_value_custom"
            android:minHeight="48dp"
            android:textSize="16sp"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:padding="8dp" />
        </LinearLayout>


    </LinearLayout>

    </LinearLayout>

    </androidx.cardview.widget.CardView>

</ScrollView>
