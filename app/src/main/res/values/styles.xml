<resources>
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> duplicitní styly Theme.Stopky a Theme.Stopky.NoActionBar -->
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> styly tlačítek ponechány -->
    <!-- <PERSON><PERSON> pro číslicová tlačítka kalk<PERSON> -->
    <style name="CalcNumButton" parent="Widget.MaterialComponents.Button">
        <item name="android:backgroundTint">#CA4418</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <!-- Styl pro operátory kalkula<PERSON>ky -->
    <style name="CalcOpButton" parent="Widget.MaterialComponents.Button">
        <item name="android:backgroundTint">@color/result_text</item>
        <item name="android:textColor">@color/operator_text</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <!-- <PERSON>yl pro funkční tlačítka kalk<PERSON> -->
    <style name="CalcFunButton" parent="Widget.MaterialComponents.Button">
        <item name="android:backgroundTint">#C58B0F</item>
        <item name="android:textColor">@color/result_text</item>
        <item name="android:textSize">16sp</item>
    </style>
    <!-- Styl pro tlačítko = -->
    <style name="CalcEqButton" parent="Widget.MaterialComponents.Button">
        <item name="android:backgroundTint">@color/result_text</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <!-- Globální styly pro texty -->
    <style name="Text.Info">
        <item name="android:textColor">@color/info_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Text.Input">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="Text.Result">
        <item name="android:textColor">@color/result_text</item>
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <!-- Obecný styl Text pro kompatibilitu -->
    <style name="Text">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textSize">16sp</item>
    </style>
    <!-- Styl pro tlačítka -->
    <style name="AppButton" parent="Widget.MaterialComponents.Button">
        <item name="android:backgroundTint">?attr/colorPrimary</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <!-- Styl pro spinner -->
    <style name="AppSpinner">
        <item name="android:backgroundTint">@color/dirty_yellow</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <!-- Styl pro toolbar -->
    <style name="AppToolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:titleTextColor">@color/white</item>
    </style>
    <!-- Styl pro tlačítka s výsledkovou barvou -->
    <style name="AppButton.Result" parent="Widget.MaterialComponents.Button">
        <item name="android:backgroundTint">@color/result_text</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <!-- Styl pro toolbar s výsledkovou barvou -->
    <style name="AppToolbar.Result" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/result_text</item>
        <item name="android:titleTextColor">@color/black</item>
    </style>
    <!-- Styl pro spinner s výsledkovou barvou -->
    <style name="AppSpinner.Result">
        <item name="android:backgroundTint">@color/result_text</item>
        <item name="android:textColor">@color/black</item>
    </style>
</resources>