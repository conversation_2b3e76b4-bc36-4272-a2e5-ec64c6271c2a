<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Stopky" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color - žluté pro aplikaci -->
        <item name="colorPrimary">@color/app_primary</item>
        <item name="colorPrimaryVariant">@color/app_primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color - tmavá systémová barva -->
        <item name="android:statusBarColor">@android:color/black</item>
        <!-- Navigation bar color - tmavá systémová barva -->
        <item name="android:navigationBarColor">@android:color/black</item>
        <!-- Tmavé ikony ve status baru pro světlé pozadí -->
        <item name="android:windowLightStatusBar">true</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.Stopky.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.Stopky.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.Stopky.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="Theme.Stopky.Settings" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/result_text</item>
        <item name="colorPrimaryVariant">@color/result_text</item>
        <item name="colorOnPrimary">@color/black</item>
    </style>

    <style name="Theme.Stopky.AlarmDialog" parent="Theme.MaterialComponents.DayNight.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>
</resources>